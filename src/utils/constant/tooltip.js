export const TOOLTIP_CONTENT = {
    user: {
        adduser: "Add a new user to the system",
    },
    common: {
        viewpastDiscussion: "View previous discussions and comments",
        delete: "Permanently remove this item",
        edit: "Modify this item's details",
        sortAsc: "Sort in ascending order (A-Z, 0-9)",
        sortDesc: "Sort in descending order (Z-A, 9-0)",
    },
    overview: {
        title: "Overview of this project",
        update: "Update project overview information",
        autoConfigure: "Automatically configure project settings",
        reConfigure: "Modify existing project configuration",
        edit: "Edit project details",
        close: "Close this panel",
        navigateTo: "Navigate to the selected section",
        moreOptions: "View additional options",
        viewPastDiscussion: "View previous discussions about this project",
        delete: "Permanently remove this project",
    },
    codeQuery: {
        title: "Query your codebase",
        content: "Analyze and query your codebase",
        Refresh: "Refresh code query results",
        Search: "Search within code query results",
        History: "View previous code queries",
        AddRepo: "Connect a new code repository",
        startQuery: "Begin a new code analysis query",
        startQueryDisabled: "Select a repository to enable queries",
    },
    codeMaintenance: {
        title: "Maintain your codebase or generate new",
        content: "Maintain and improve your codebase",
        History: "View code maintenance history",
        StartSession: "Begin a new maintenance session",
        StartSessionDisabled: "Select a repository to enable maintenance",
    },
    Requirements: {
        title: "Project requirements",
        create: "Create a new project requirement",
        update: "Update an existing requirement",
        autoConfigure: "Automatically generate requirements",
    },
    WorkItems: {
        title: "Items to be completed",
        create: "Create a new work item",
        update: "Update an existing work item",
        viewPastDiscussion: "View previous discussions about this work item",
    },
    Architecture: {
        title: "System architecture",
        autoConfigure: "Automatically configure architecture components",
        tabs: {
            architecture_requirement: "Manage architecture requirements and specifications",
            system_context: "View and edit system context diagrams",
            container: "Configure container diagrams and dependencies",
            component: "Manage component diagrams and relationships",
            design: "Access detailed design specifications",
            interfaces: "Configure system interfaces and integration points",
            software_architecture: "View complete software architecture documentation",
            prd: "Access product requirements documentation",
            api_docs: "View API specifications and documentation"
        },
        requirements: {
            update: "Update architecture requirement",
            create: "Create new architecture requirement",
            edit: "Modify requirement details",
            view: "View requirement details",
            autoConfigure: "Automatically generate requirements",
            viewPastDiscussion: "View previous discussions about this requirement",
            delete: "Remove this requirement",
        },
        system_context: {
            update: "Update system context diagram",
            create: "Create new system context diagram",
            viewPastDiscussion: "View previous discussions about system context",
        },
        component: {
            viewPastDiscussion: "View previous discussions about components",
        },
        container: {
            pastCodegen: "View previously generated code",
            viewPastDiscussion: "View previous discussions about containers",
            delete: "Remove this container",
            edit: "Modify container details",
        },
        design: {
            update: "Update design specifications",
            create: "Create new design document",
            viewComponents: "View associated components",
            generateCode: "Generate code based on this design",
            pastCodegen: "View previously generated code",
            viewPastDiscussion: "View previous discussions about this design",
            delete: "Remove this design",
            edit: "Modify design details",
        },
        interfaces: {
            update: "Update interface specifications",
            create: "Create new interface definition",
            viewPastDiscussion: "View previous discussions about interfaces",
            edit: "Modify interface details",
            delete: "Remove this interface", 
        },
        sad: {
            update: "Update Software Architecture Document",
            create: "Create new Software Architecture Document",
            updateDoc: "Update SAD documentation content",
        }
    },
    marketReasearch: {
        title: "Research on market trends",
        upload: "Upload market research document",
    },
    Figma: {
        title: "Design your user interface",
        Create_New: "Add a new UI/UX design",
        Reload_Designs: "Refresh design previews",
    },
    Document: {
        title: "Project documentation",
        create: "Create a new document",
        upload: "Upload an existing document",
        download: "Download selected document",
    },
    Deployement: {
        title: "Deploy your application",
        Create_New: "Deploy a new version",
        view_history: "View deployment history",
        rollback: "Rollback to previous version",
    },
    testCase: {
        title: "Define test scenarios",
        Create_New: "Create a new test case",
        run: "Execute selected test case",
        view_results: "View test case results",
    },
    testExecution: {
        title: "Run your test cases",
        Create_New: "Execute a new test run",
        view_history: "View test execution history",
        generate_report: "Generate test execution report",
    },
};