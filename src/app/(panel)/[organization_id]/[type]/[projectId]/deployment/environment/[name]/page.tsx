
// src/app/(panel)/[organization_id]/[type]/[projectId]/deployment/environment/[name]/page.tsx
//@ts-nocheck
'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import { User, Terminal } from 'lucide-react';

const EnvironmentDetailsPage = () => {
  const params = useParams();
  const router = useRouter();

  const environmentName = params.name;
  const displayName = ''
  // environmentName ? 
  //   environmentName.charAt(0).toUpperCase() + environmentName.slice(1) 
  //   : '';

  const handleBackToDashboard = () => {
    router.push(`/${params.type}/${params.projectId}/deployment`);
  };

  const mockInstances = [
    {
      id: '92',
      revision: '2501733eb23342ad1df06c68a66f1',
      timestamp: '25 Apr, 2024 at 10:51:58 Local Time',
      steps: [
        { completed: true },
        { completed: true },
        { completed: true },
        { completed: true },
        { completed: true }
      ]
    },
    {
      id: '91',
      revision: '2501733eb23342ad1df06c68a66f1',
      timestamp: '25 Apr, 2024 at 09:33:04 Local Time',
      steps: [
        { completed: true },
        { completed: true },
        { completed: true },
        { completed: false },
        { completed: false }
      ]
    },
    {
      id: '90',
      revision: '2501733eb23342ad1df06c68a66f1',
      timestamp: '24 Apr, 2024 at 17:42:10 Local Time',
      steps: [
        { completed: false },
        { completed: false },
        { completed: false },
        { completed: false },
        { completed: false }
      ]
    },
    {
      id: '89',
      revision: 'f595dc11649ecb6b019b352c1bccc',
      timestamp: '22 Apr, 2024 at 10:19:42 Local Time',
      steps: [
        { completed: true },
        { completed: true },
        { completed: true },
        { completed: true },
        { completed: true }
      ]
    }
  ];

  return (
    <div>
      {/* Breadcrumb */}
      <div className="h-[36px] flex items-center px-4 border-b">
        <div className="flex items-center typography-body-sm">
          <button 
            onClick={handleBackToDashboard}
            className="text-gray-500 hover:text-[#2A3439] cursor-pointer"
          >
            Deployment Environment
          </button>
          <span className="mx-2 text-gray-400">›</span>
          <span className="text-[#2A3439]">{displayName}</span>
        </div>
      </div>

      {/* Instances Table */}
      <div className="p-6">
        <div className="border border-[#cfcfd2] rounded-md">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[#cfcfd2]">
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">Instance</th>
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">Path</th>
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">Step 2</th>
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">build</th>
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">Step 4</th>
                <th className="text-left py-2 px-4 typography-body-sm font-weight-medium text-[#2A3439]">deploy-to-dev-env</th>
              </tr>
            </thead>
            <tbody>
              {mockInstances.map((instance) => (
                <tr key={instance.id} className="border-b border-[#cfcfd2] last:border-b-0">
                  <td className="py-4 px-4 text-[#2A3439] typography-body-sm">{instance.id}</td>
                  <td className="py-4 px-4">
                    <div className="space-y-1">
                      <div className="typography-caption">Revision: {instance.revision}</div>
                      <div className="typography-caption text-gray-500">{instance.timestamp}</div>
                      <div className="flex gap-2 mt-1">
                        <Terminal className="w-4 h-4 text-gray-400" />
                        <User className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </td>
                  {instance.steps.map((step, index) => (
                    <td key={index} className="py-4 px-4">
                      <div className="w-[88px] h-[16px] relative">
                        <div className={`w-full h-[4px] absolute top-[6px] ${
                          step.completed ? 'bg-green-500' : 'bg-gray-200'
                        }`} />
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EnvironmentDetailsPage;
