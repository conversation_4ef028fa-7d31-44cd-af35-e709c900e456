//@ts-nocheck
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import DiscussionModal from '../components/Discussion';
import Overview from '../components/Overview';
import DeploymentEnvironment from '../components/DeploymentEnvironment';

const NoSSR = dynamic(
  () => import("@/components/Chart/MermaidChart"),
  { ssr: false }
);

interface TabProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
}

const TabComponent: React.FC<TabProps> = ({ label, isActive, onClick }) => (
  <div
    role="tab"
    aria-selected={isActive}
    className={`flex items-center gap-3 p-2 px-4 cursor-pointer transition-all ${
      isActive
        ? "text-[#1c64f2] bg-white rounded-md shadow-md border border-gray-200"
        : "text-gray-500 bg-[#f9f9fb]"
    }`}
    onClick={onClick}
  >
    {label}
  </div>
);

const FrontendPage = () => {
  const [showData, setShowData] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [isDiscussionOpen, setIsDiscussionOpen] = useState(false);

  const [expandedSections, setExpandedSections] = useState({
    summary: true,
    overview: true,
  });

  const router = useRouter();
  const pathname = usePathname();

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "environment", label: "Deployment Environment" },
    { id: "visualization", label: "Infrastructure visualization" },
  ];

 

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleCreateInfrastructure = () => {
    setIsDiscussionOpen(true);
  };

  const renderActionButtons = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <>
          </>
        );
      case 'environment':
        return (
          <>
          </>
        );
      default:
        return null;
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <Overview showData={showData} />;
      case 'environment':
        return <DeploymentEnvironment />;
      case 'visualization':
        return <div>Infrastructure Visualization Content</div>;
      default:
        return null;
    }
  };
 

  return (
    <div className="h-screen flex flex-col bg-white mt-4">
      {/* Header Section - Fixed */}
      <div className="flex-none border-b bg-white">
        <div className="flex justify-between items-start px-4 py-2">
          {/* Tabs Container */}
          <div className="bg-[#f9f9fb] rounded-md p-1">
            <div className="flex gap-2">
              {tabs.map((tab) => (
                <TabComponent
                  key={tab.id}
                  label={tab.label}
                  isActive={activeTab === tab.id}
                  onClick={() => handleTabClick(tab.id)}
                />
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {renderActionButtons()}
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="h-[36px] flex items-center px-4">
          <div className="flex items-center typography-body-sm">
            <Link href="." className="text-gray-500 hover:text-[#2A3439]">
              Home
            </Link>
            <span className="mx-2 text-gray-400">›</span>
            <span className="text-[#2A3439]">Frontend Infrastructure</span>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-4 py-8">
          <div className="mx-auto">
 
            <div className="cursor-pointer">
              <div
                onClick={() => setShowData(!showData)}
                className="typography-body-sm cursor:pointer "
              >
                Toggle {showData ? "Empty" : "Data"} State
              </div>
            </div>

            {/* Conditional Rendering */}
           {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        {renderTabContent()}
      </div>
          </div>
        </div>
      </div>
       {/* Discussion Modal */}
       <DiscussionModal
        isOpen={isDiscussionOpen}
        onClose={() => setIsDiscussionOpen(false)}
        nodeId="placeholder-id"
        nodeType="Infrastructure"
      />
    </div>
  );
};

export default FrontendPage;
