// pages/dashboard/organizations/tracker.jsx
"use client";
import React, { useState, useEffect } from "react";
import {
  Activity,
  Users,
  Clock,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  ChevronDown,
} from "lucide-react";
import {
  getDashboardOverview,
  getAllTenants,
  getGeographicData,
} from "@/utils/api";
import DashboardCharts from "@/components/dashboard/DashboardCharts";
import LoadingSpinner from "@/components/Loaders/LoadingSpinner";
export default function Page() {
  // State management - Set default tenant to 'b2c'
  const [selectedTenant, setSelectedTenant] = useState("b2c");
  const [timeRange, setTimeRange] = useState(7);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Data state
  const [dashboardData, setDashboardData] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [geographicData, setGeographicData] = useState(null);
  const [previousPeriodData, setPreviousPeriodData] = useState(null);

  // Time range options
  const timeRangeOptions = [
    { value: 1, label: "Last 24 hours" },
    { value: 7, label: "Last 7 days" },
    { value: 30, label: "Last 30 days" },
    { value: 90, label: "Last 90 days" },
  ];

  // Fetch tenants and update dropdown options
  const fetchTenants = async () => {
    try {
      const tenantsData = await getAllTenants();
      const tenantsList = tenantsData?.tenants || [];
      setTenants(tenantsList);

      // Only update selectedTenant if it's not already set to 'b2c' or if 'b2c' doesn't exist
      if (!tenantsList.find((t) => t.id === "b2c") && tenantsList.length > 0) {
        // If 'b2c' doesn't exist, use the first available tenant
        setSelectedTenant(tenantsList[0].id);
      }
    } catch (err) {
      console.error("Error fetching tenants:", err);
      setError("Failed to load tenants");
    }
  };

  // Fetch dashboard data
  const fetchDashboardData = async (showRefreshLoader = false) => {
    if (!selectedTenant) return;

    try {
      if (showRefreshLoader) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // Fetch current and previous period data for comparison
      const [overviewData, geoData] = await Promise.all([
        getDashboardOverview(selectedTenant, timeRange),
        getGeographicData(selectedTenant, timeRange),
      ]);


      setDashboardData(overviewData);
      setGeographicData(geoData);

      setPreviousPeriodData(overviewData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial load - fetch tenants first, then data
  useEffect(() => {
    fetchTenants();
  }, []);

  // Fetch dashboard data when tenant or timeRange changes
  useEffect(() => {
    if (selectedTenant) {
      fetchDashboardData();
    }
  }, [selectedTenant, timeRange]);

  // Event handlers
  const handleTenantChange = (e) => {
    setSelectedTenant(e.target.value);
  };

  const handleTimeRangeChange = (e) => {
    setTimeRange(parseInt(e.target.value));
  };

  const handleRefresh = () => {
    fetchDashboardData(true);
  };


  const calculateTrend = (current, previous, metric) => {
    // Add null checks for both current and previous data
    if (!current || !current.summary || !previous || !previous.summary) {
      return { value: 0, isPositive: true };
    }

    const currentValue = current.summary[metric] || 0;
    const previousValue = previous.summary[metric] || 0;

    if (previousValue === 0) return { value: 0, isPositive: true };

    const change = ((currentValue - previousValue) / previousValue) * 100;
    return {
      value: Math.abs(change).toFixed(1),
      isPositive: change >= 0,
    };
  };


  // Loading state
  if (loading && !dashboardData) {
    return <LoadingSpinner />;
  }

  // Error state
  if (error && !dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="typography-heading-4 font-weight-semibold text-red-600 mb-2">
            Error Loading Dashboard
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => fetchDashboardData()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="w-8 h-8 text-blue-600" />
              </div>
              <div className="ml-3">
                <h1 className="typography-heading-4 font-weight-semibold text-gray-900">
                  API Analytics Dashboard
                </h1>
                <p className="typography-body-sm text-gray-500">
                  Real-time monitoring & insights
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Tenant Selector */}

              <div className="relative">
                <select
                  value={selectedTenant}
                  onChange={handleTenantChange}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 typography-body-sm font-weight-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                  disabled={tenants.length === 0}
                  style={{ backgroundImage: "none" }} // Ensure no default browser styling
                >
                  {tenants.length === 0 ? (
                    <option>Loading tenants...</option>
                  ) : (
                    <>
                      {tenants.find((t) => t.id === "b2c") && (
                        <option key="b2c" value="b2c">
                          B2C Platform (
                          {tenants.find((t) => t.id === "b2c").total_requests}{" "}
                          requests)
                        </option>
                      )}
                      {tenants
                        .filter((t) => t.id !== "b2c")
                        .map((tenant) => (
                          <option key={tenant.id} value={tenant.id}>
                            {tenant.name} ({tenant.total_requests} requests)
                          </option>
                        ))}
                    </>
                  )}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>

              {/* Time Range Selector - Fixed */}
              <div className="relative">
                <select
                  value={timeRange}
                  onChange={handleTimeRangeChange}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 typography-body-sm font-weight-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                  style={{ backgroundImage: "none" }} // Ensure no default browser styling
                >
                  {timeRangeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>
              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
              >
                <RefreshCw
                  className={`w-4 h-4 ${refreshing ? "animate-spin" : ""}`}
                />
                <span>Refresh</span>
              </button>

            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {dashboardData ? (
          <>
            {/* Summary Metrics */}
            <SummaryMetrics
              data={dashboardData}
              previousData={previousPeriodData}
              calculateTrend={calculateTrend}
            />

            {/* Charts Section */}
            <DashboardCharts
              dailyStats={dashboardData.daily_stats}
              hourlyTrends={dashboardData.hourly_trends}
              statusDistribution={dashboardData.status_distribution}
              topEndpoints={dashboardData.top_endpoints}
              topUsers={dashboardData.top_users}
              geographicData={geographicData?.geographic_data || []}
              timeRange={timeRange}
            />
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">
              No data available for the selected tenant and time range.
            </p>
          </div>
        )}
      </main>
    </div>
  );
}


function SummaryMetrics({ data, previousData, calculateTrend }) {
  // Add null check for data and data.summary
  if (!data || !data.summary) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="bg-white rounded-xl shadow-sm p-6 animate-pulse"
          >
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    );
  }

  const formatNumber = (num) => {
    if (!num && num !== 0) return "0";
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const metrics = [
    {
      title: "Total Requests",
      value: formatNumber(data.summary.total_requests || 0),
      icon: Activity,
      color: "blue",
      metric: "total_requests",
    },
    {
      title: "Unique Users",
      value: data.summary.unique_users || 0,
      icon: Users,
      color: "green",
      metric: "unique_users",
    },
    {
      title: "Avg Response Time",
      value: `${Math.round(data.summary.avg_response_time || 0)}ms`,
      icon: Clock,
      color: "yellow",
      metric: "avg_response_time",
      isLowerBetter: true,
    },
    {
      title: "Error Rate",
      value: `${(data.summary.error_rate || 0).toFixed(2)}%`,
      icon: AlertTriangle,
      color: "red",
      metric: "error_rate",
      isLowerBetter: true,
    },
  ];

  const colorClasses = {
    blue: { bg: "bg-blue-100", text: "text-blue-600", value: "text-blue-600" },
    green: {
      bg: "bg-green-100",
      text: "text-green-600",
      value: "text-green-600",
    },
    yellow: {
      bg: "bg-yellow-100",
      text: "text-yellow-600",
      value: "text-yellow-600",
    },
    red: { bg: "bg-red-100", text: "text-red-600", value: "text-red-600" },
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {metrics.map((metric, index) => {
        const colors = colorClasses[metric.color];
        const trend = calculateTrend(data, previousData, metric.metric);

        // For metrics where lower is better, invert the trend logic
        const isGoodTrend = metric.isLowerBetter
          ? !trend.isPositive
          : trend.isPositive;
        const TrendIcon = trend.isPositive ? TrendingUp : TrendingDown;
        const trendColor = isGoodTrend ? "text-green-600" : "text-red-600";
        const trendPrefix = trend.isPositive ? "+" : "-";

        return (
          <div
            key={index}
            className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-all metric-card"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="typography-body-sm font-weight-medium text-gray-600">
                  {metric.title}
                </p>
                <p className={`typography-heading-2 font-weight-bold mt-1 ${colors.value}`}>
                  {metric.value}
                </p>
                <p className={`typography-body-sm mt-1 flex items-center ${trendColor}`}>
                  <TrendIcon className="w-4 h-4 mr-1" />
                  {trend.value > 0
                    ? `${trendPrefix}${trend.value}% vs last period`
                    : "No comparison data"}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${colors.bg}`}>
                <metric.icon className={`w-6 h-6 ${colors.text}`} />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
