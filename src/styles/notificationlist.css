.notification-container {
  @apply flex flex-col h-full bg-custom-bg-notification-default;
  }
  .notification-scroll-area {
  @apply overflow-y-auto flex-grow;
  }
  /* Date header */
  .notification-date-header {
  @apply font-bold text-primary-500 capitalize;
  }
  /* Group container */
  .notification-group {
  @apply p-custom-4 pb-0 space-y-3;
  }
  /* Notification item */
  /* Notification content layout */
  /* Bell icon container */
  /* Unread indicator dot */
  /* Text content */
  /* Delete button */
  /* Footer */
  .notification-footer {
  @apply flex justify-center items-center py-custom-2 bg-white border-t border-custom-border hover:bg-custom-bg-notification-unread;
  }
  .mark-all-read-button {
  @apply font-bold text-primary-500 text-center cursor-pointer hover:underline;
  }
  /* Clear all button */
  .clear-all-button {
  @apply absolute bottom-16 right-custom-4 bg-destructive-500 hover:bg-destructive-600 text-white font-bold py-custom-3 px-custom-3 rounded-custom-full shadow-lg transition-colors duration-200 flex items-center space-x-custom-2 text-custom-sm;
  }
  .clear-all-icon {
  @apply size-custom-4 transition-transform duration-200 ease-in-out group-hover:rotate-90;
  }