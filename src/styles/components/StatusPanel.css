@tailwind base;
@tailwind components;
@tailwind utilities;
@layer components {
  /* Status Panel Header */
  .status-panel-header {
    @apply w-full bg-background shadow-sm z-20 border-b border-border transition-all duration-300 mb-2;
    background-image: linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted) / 0.5));
    top: 0;
  }
  /* Add subtle hover effect */
  .status-panel-header:hover .header-section-middle {
    @apply bg-muted rounded-md px-2;
  }
  /* Add subtle animation on hover */
  .status-panel-header:hover {
    @apply shadow;
  }
  .status-panel-header-content {
    @apply flex items-center justify-between px-2 py-0.5;
  }
  /* Sections */
  .header-section {
    @apply flex items-center;
  }
  .header-section-left {
    @apply flex-shrink-0 w-1/5;
  }
  .header-section-middle {
    @apply flex-grow flex justify-center;
  }
  .header-section-right {
    @apply flex-shrink-0 w-1/3 justify-end;
  }
  /* Project Title */
  .project-panel-title {
    @apply flex items-center bg-gray-50 px-2 py-2 rounded-md border border-gray-100;
  }
  .project-title-text {
    @apply text-sm font-medium truncate max-w-[200px] text-gray-900;
  }
  /* Connection Status */
  .connection-status {
    @apply flex items-center justify-center ml-1;
  }
  /* Progress Bar */
  .progress-container {
    @apply relative w-40 h-3 bg-gray-100 rounded-full overflow-hidden shadow-inner border border-gray-200;
  }
  .progress-bar {
    @apply h-full rounded-full transition-all duration-300;
  }
  .progress-bar-blue {
    @apply bg-primary-500;
  }
  .progress-bar-green {
    @apply bg-green-500;
  }
  /* Status Badge */
  .status-badge {
    @apply px-3 py-1.5 text-xs font-semibold rounded-md border;
  }
  .status-badge-in-progress {
    @apply bg-primary-100 text-primary-700 border-primary-300;
  }
  .status-badge-completed {
    @apply bg-green-100 text-green-700 border-green-300;
  }
  .status-badge-other {
    @apply bg-gray-100 text-gray-700 border-gray-300;
  }
  /* Auto Navigate Toggle */
  .auto-navigate-container {
    @apply flex items-center bg-gray-50 px-2 py-1 rounded-md border border-gray-200 transition-all duration-200 hover:bg-gray-100 shadow-sm;
  }
  .auto-navigate-container:hover {
    @apply shadow-md;
  }
  .auto-navigate-label {
    @apply mr-2 text-xs font-medium text-gray-700;
  }
  .toggle-switch {
    @apply relative inline-flex items-center h-5 rounded-full w-10 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-gray-300;
  }
  .toggle-switch-knob {
    @apply inline-block w-4 h-4 transform bg-white rounded-full shadow-md transition-all duration-300 ease-in-out;
  }
  /* Add subtle glow effect when toggle is active */
  .toggle-switch[aria-pressed="true"] {
    @apply ring-1 ring-gray-300;
  }
  .toggle-switch[aria-pressed="true"] .toggle-switch-knob {
    @apply bg-white shadow-lg;
  }
  /* Action Buttons */
  .action-buttons-container {
    @apply flex bg-gray-50 rounded-md gap-1 p-1 border border-gray-200 shadow-sm transition-all duration-200;
  }
  .action-buttons-container:hover {
    @apply shadow-md bg-gray-100;
  }
  .action-button {
    @apply p-1.5 rounded hover:bg-gray-100 transition-all duration-200;
  }
  .action-button:hover {
    @apply shadow-sm transform scale-105;
  }
  .action-icon {
    @apply text-gray-500 hover:text-gray-700 transition-colors duration-200;
  }
  .action-icon-danger {
    @apply text-red-500 hover:text-red-700 transition-colors duration-200;
  }
  /* Full execution panel styles */
  #statusPanel {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Important for flex child to respect parent height */
    position: relative;
  }
  #statusPanel > div:not(.status-panel-header) {
    flex: 1;
    overflow: auto;
    min-height: 0; /* Important for nested flex containers */
    width: 100%;
  }
}