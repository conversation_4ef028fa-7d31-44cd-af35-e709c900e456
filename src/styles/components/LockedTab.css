@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

  .locked-tab {
    @apply opacity-70 hover:opacity-85 transition-all duration-300;
    position: relative;
    overflow: hidden;
  }

  .locked-tab:hover {
    box-shadow: 0 0 0 1px rgba(var(--primary), 0.2);
  }

  .locked-tab::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.05), rgba(var(--primary), 0.05));
    pointer-events: none;
  }

  .locked-tab-content {
    @apply relative;
  }

  .locked-tab-overlay {
    @apply absolute inset-0 bg-white bg-opacity-10 backdrop-blur-[1px] z-0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(var(--primary), 0.05));
  }

  .locked-tab-text {
    @apply relative z-10 opacity-75;
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
  }
}
