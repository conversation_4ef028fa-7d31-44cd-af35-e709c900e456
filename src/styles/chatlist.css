.chat-list {
  @apply bg-white h-full flex flex-col;
}



.new-project-button {
  @apply ml-custom-sm rounded;
}

.plus-icon {
  @apply text-custom-lg font-medium mr-custom-xs;
}

.chat-list-container {
  @apply flex-1 overflow-y-auto;
  /* scrollbar-width: none; Firefox */
 /* -ms-overflow-style: none;  IE and Edge */
}
.chat-list-container::-webkit-scrollbar {
  /* display: none;  Chrome, Safari, Opera */
}

/* For ProjectItem component, update the container class to be full width */
.project-hover-container {
  width: 100%;
  margin-right: 0;
  padding-right: 8px; /* Add some padding to ensure the kebab menu isn't too close to the edge */
}


.chat-item {
  @apply flex items-center justify-between p-custom-lg border-t border-custom-border-input cursor-pointer space-x-2;
}



.chat-item:hover {
  @apply bg-custom-bg-secondary;
}



.chat-item.selected {
  @apply bg-custom-bg-muted;
}


.chat-info {
  @apply flex items-center gap-custom-lg flex-grow;
}

.project-info {
  @apply flex flex-col;
}

.project-title {
  @apply font-semibold text-custom-base text-custom-text-primary;
}


.project-item-title {
  @apply font-semibold text-custom-md text-custom-text-primary;
}


.chat-icon {
  @apply flex-shrink-0;
}

.chat-details {
  @apply flex-grow;
}

.chat-name {
  @apply font-semibold text-custom-md text-custom-text-primary;
}



.chat-date {
  @apply text-custom-text-muted text-custom-xs;
}


.chevron-icon {
  @apply text-custom-text-muted text-custom-lg ml-custom-lg;
}


.chat-menu {
  @apply flex-shrink-0;
}

.menu-dropdown {
  @apply absolute right-0 top-full mt-custom-sm w-48 bg-white border border-custom-border rounded shadow-custom-card z-10;
}



.menu-item {
  @apply px-custom-lg py-custom-sm text-custom-base text-custom-text-secondary cursor-pointer hover:bg-custom-bg-secondary;
}


.rename-input {
  @apply w-full px-custom-sm py-custom-xs text-custom-base border border-custom-border-input rounded shadow-custom-list;
}



.no-chats {
  @apply text-center p-custom-lg text-custom-text-muted;
}



.preload-chat-list {
  @apply h-full flex flex-col;
  height: calc(100vh - 120px);
  min-height: 500px;
  width: 100% !important;
}

.error-message {
  @apply text-center p-custom-lg text-destructive text-custom-base;
}

/* Project tabs styles */
.project-tabs {
  display: flex;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Dark theme project tabs */
.project-tabs.bg-\[#231f20\] {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.project-tab {
  flex: 1;
  height: 40px;
  padding: 10px 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border: none;
  cursor: pointer;
  font-family: 'Hind', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: center;
  color: #6e6e6e;
  transition: all 0.2s ease-in-out;
}

.project-tab:hover {
  color: #6e6e6e;
}

.project-tab.active {
  background: rgba(242, 106, 27, 0.1);
  border-bottom: 2px solid #f26a1b;
  color: #f26a1b;
}

/* Dark theme tab text colors */
.project-tabs.bg-\[#231f20\] .project-tab {
  color: #a1a1aa; /* gray-400 */
}

.project-tabs.bg-\[#231f20\] .project-tab:hover {
  color: #d1d5db; /* gray-300 */
}

.project-tabs.bg-\[#231f20\] .project-tab.active {
  background: rgba(242, 106, 27, 0.2);
  color: #ffffff;
}



/* Project list header */
.project-list-header {
  @apply p-4 flex flex-col;
}

.project-list-title {
  @apply text-2xl font-medium text-gray-800 mb-6;
}


/* Project list hover effect */
#sidebar-project-list > div:hover:not(.empty-state-container) {
  @apply relative overflow-hidden;
  width: 100%;
  padding-right: 0;
}

/* Light theme hover (default) */
#sidebar-project-list > div:hover:not([class*="bg-[#231f20]"]):not([class*="bg-gray-700"]):not(.empty-state-container) {
  @apply bg-white;
}

/* Dark theme hover - when container has dark theme classes */
#sidebar-project-list > div:hover[class*="bg-[#231f20]"]:not(.empty-state-container),
#sidebar-project-list > div:hover[class*="bg-gray-700"]:not(.empty-state-container) {
  background-color: rgb(55 65 81) !important; /* gray-700 */
}

#sidebar-project-list > div:hover:not(.empty-state-container)::after {
  content: '';
  @apply w-[156px] h-[87px] absolute right-0 top-[-71px] bg-[#f26a1b] rounded-full blur-[120px] pointer-events-none;
}

#sidebar-project-list > div:hover:not(.empty-state-container) .size-\[9\.67px\] {
  @apply bg-[#f26a1b];
}

.project-item-details {
  @apply flex flex-col;
}

.project-item-title {
  @apply text-base font-medium text-gray-800;
}



.project-item-subtitle {
  @apply text-sm text-gray-500 flex items-center mt-1;
}


.project-item-subtitle span {
  @apply mx-1;
}

.project-avatar {
  @apply h-8 w-8 rounded-full bg-purple-500 text-white flex items-center justify-center text-base font-medium ml-4;
}

.project-action-icon {
  @apply h-5 w-5 text-gray-500 ml-3;
}


/* Create New Project button */
.create-new-project-wrapper {
  @apply px-4 py-3 border-t border-gray-200 mt-auto;
}


.create-new-project-btn {
  @apply w-full py-2 px-4 bg-orange-500 hover:bg-orange-600 text-white text-sm font-medium rounded-md
  flex justify-center items-center border-0;
}

/* Custom search input */
.project-search-container {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-1;
}


.project-search-input {
  @apply w-full p-2 bg-white rounded-md border border-gray-300 text-gray-600 placeholder-gray-400;
}



.chat-list .search-container {
  border: none;
  box-shadow: none;
  background: transparent;
}

/* Keep the input border */
.chat-list .search-container input {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
}



/* Create project button container to ensure it stays at the bottom */
.create-project-button-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}



