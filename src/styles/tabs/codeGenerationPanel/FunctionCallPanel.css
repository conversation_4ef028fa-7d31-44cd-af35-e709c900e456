@tailwind base;
@tailwind components;
@tailwind utilities;
@layer components {
  /* Main Container */
  .function-calls-container {
    @apply p-4 space-y-4;
  }
  .function-calls-empty {
    @apply text-center flex justify-center h-96 items-center;
  }
  /* Function Call Item */
  .function-call-item {
    @apply p-4 mb-4 cursor-pointer transition-colors rounded-lg
           bg-gray-50 hover:bg-gray-100 border border-gray-200;
  }
  .function-call-item-latest {
    @apply bg-primary-50 hover:bg-primary-100 border-primary-200;
  }
  .function-call-content {
    @apply mb-4 flex justify-between items-start;
  }
  .function-call-info {
    @apply flex-1;
  }
  .function-call-field {
    @apply mb-2 flex last:mb-0;
  }
  .function-call-label {
    @apply font-semibold w-32;
  }
  .function-call-value {
    @apply flex-1 text-primary-600;
  }
  .function-call-value-latest {
    @apply text-primary-700;
  }
  .function-call-reason {
    @apply flex-1 text-green-600;
  }
  .function-call-reason-latest {
    @apply text-green-700;
  }
  .function-call-observation {
    @apply flex-1 text-yellow-600;
  }
  .function-call-observation-latest {
    @apply text-yellow-700;
  }
  .function-call-timestamp {
    @apply text-sm text-gray-500;
  }
  /* Modal Styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50
           flex justify-center items-center p-4;
  }
  .modal-container {
    @apply bg-white rounded-lg max-w-[600px] w-full
           max-h-[80vh] min-h-[300px] mt-[10vh]
           flex flex-col mx-auto
           shadow-xl;
  }
  .modal-title {
    @apply text-2xl font-bold text-gray-800;
  }
  /* Modal Arguments */
  .modal-argument {
    @apply mb-4 last:mb-0 bg-gray-50 p-4 rounded-lg;
  }
  .modal-argument-key {
    @apply text-primary-600 block mb-2;
  }
  .modal-argument-value {
    @apply text-gray-700;
  }
  .modal-argument-list {
    @apply list-disc list-inside ml-4 space-y-1;
  }
}