@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .terminal-wrapper {
    @apply relative;
  }

  .terminal-empty {
    @apply text-center flex justify-center h-96 items-center;
  }

  .terminal-container {
    @apply relative bg-gray-900 p-5 rounded-md shadow-md 
           min-h-[80vh] max-h-[80vh];
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .terminal-container::-webkit-scrollbar {
    @apply w-2;
  }

  .terminal-container::-webkit-scrollbar-track {
    @apply bg-gray-900;
  }

  .terminal-container::-webkit-scrollbar-thumb {
    @apply bg-gray-400/30 rounded-full hover:bg-gray-400/40 transition-colors;
  }

  .terminal-content {
    @apply m-0 whitespace-pre-wrap break-words leading-relaxed;
  }

  .terminal-cursor {
    @apply inline-block text-green-500 ml-1;
    animation: blink 1s step-end infinite;
  }

  .terminal-prefix {
    @apply text-green-500 font-medium;
  }

  .terminal-text {
    @apply text-sky-300;
  }
}

/* Animation */
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}