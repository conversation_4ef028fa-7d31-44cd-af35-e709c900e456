/* design detail page in design tab css alignment start */
/* full screen loder css for generate code tab starts*/
.loader-overlay{
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20
}
.loader-container {
    @apply bg-white p-5 rounded-lg shadow-lg w-[97%] h-[93%]
}
.loader-content{
    @apply px-4 py-2 text-center list-none h-32 mt-9 flex flex-col justify-center items-center h-full
}
.loader-spinner{
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-3
}
.loader-text {
    @apply text-center
}
/* full screen loder css for generate code tab ends */
/* .design-details-content-wrapper {
    @apply flex flex-col h-full max-w-full overflow-x-hidden
} */
.design-details-content-wrapper {
    @apply overflow-y-auto  max-h-[74vh] text-black text-[15px] font-normal leading-[21px] tracking-[0.2px]
}
/* .design-details-content-sub-wrapper {
    @apply flex-grow overflow-y-hidden 
} */
.design-details-content-sub-wrapper {
    @apply mb-6 bg-white p-0 
}
.design-details-container {
    /* @apply p-4 space-y-4 */
    @apply space-y-4
}
.design-details-header-wrapper {
    @apply flex sticky top-0 p-2 justify-between  items-center border rounded-lg h-[80px] z-10
}
.design-details-header-sub-wrapper {
    @apply flex items-center  max-h-full  ml-2
}
/* .design-details-header-container {
    @apply flex items-center flex-grow
} */
.design-details-heading-title{
    @apply text-[16px] font-bold  text-[#2A3439] whitespace-normal ml-4 
}
.design-details-heading-badge {
    @apply py-1 ml-2 mr-2
}
.design-details-header-button-wrapper {
    @apply flex items-center space-x-2 mr-2 ml-2 flex-shrink-0
}
.design-details-additional-design-section {
    @apply mt-4 
}
.design-details-content-sub-wrapper{
    /* @apply p-4 */
}
.design-details-not-found {
    @apply flex justify-center  items-center text-center
}