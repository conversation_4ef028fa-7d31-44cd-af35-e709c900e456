/* Sidebar Index start */
.redirecting-overlay {
  @apply fixed inset-0 flex justify-center items-center;
}
.sidebar-container {
  @apply relative flex h-screen ;
}
.sidebar {
  @apply relative left-0 top-0 flex h-screen flex-col border-r border-custom-border-sidebar bg-custom-bg-muted text-custom-text-primary transition-[width] duration-300;
}
.sidebar.dark-theme {
  @apply bg-[#231f20] border-gray-700 text-white;
}
/* Responsive sidebar widths */
.sidebar.collapsed {
  @apply w-custom-16;
}
.sidebar:not(.collapsed) {
  @apply w-custom-64;
}
/* Responsive adjustments for different screen sizes */
@media (max-width: 1536px) {
  .sidebar:not(.collapsed) {
    @apply w-56;
  }
}
@media (max-width: 1280px) {
  .sidebar:not(.collapsed) {
    @apply w-52;
  }
}
@media (max-width: 1024px) {
  .sidebar:not(.collapsed) {
    @apply w-48;
  }
  .sidebar.collapsed {
    @apply w-14;
  }
}
.sidebar-header {
  @apply flex items-center justify-center px-0 py-custom-3 border-b border-custom-border-sidebar w-full;
}
.sidebar.dark-theme .sidebar-header {
  @apply border-gray-700;
}
.logo-container {
  @apply relative h-8 flex items-center;
}
.logo-small {
  @apply w-custom-12;
}
.logo-large {
  @apply w-custom-44;
}
/* Responsive logo sizes */
@media (max-width: 1536px) {
  .logo-large {
    @apply w-40;
  }
}
@media (max-width: 1280px) {
  .logo-large {
    @apply w-36;
  }
}
@media (max-width: 1024px) {
  .logo-large {
    @apply w-32;
  }
  .logo-small {
    @apply w-10;
  }
}
.logo-image {
  @apply size-custom-4 hover:animate-spin;
}
.sidebar-nav {
  @apply flex flex-col overflow-y-auto transition-all duration-300 ease-linear w-full;
  @apply pt-3;
}
.menu-group {
  @apply mt-custom-2;
}
.group-title {
  @apply mb-custom-5 text-custom-md font-medium text-custom-text-muted;
}
.sidebar.dark-theme .group-title {
  @apply text-gray-300;
}
/* Responsive font sizes */
@media (max-width: 1280px) {
  .group-title {
    @apply text-sm;
  }
  .sidebar-link:not(.collapsed) span {
    @apply text-sm;
  }
}
@media (max-width: 1024px) {
  .group-title {
    @apply text-xs;
  }
  .sidebar-link:not(.collapsed) span {
    @apply text-xs;
  }
}
.menu-list {
  @apply mb-custom-5 flex flex-col;
  @apply gap-2.5;
}
.menu-icon {
  @apply size-custom-5 text-custom-text-notification;
}
.sidebar.dark-theme .menu-icon {
  @apply text-gray-200;
}
.sidebar.dark-theme .menu-list-icon {
  @apply text-gray-200;
}
.sidebar-footer {
  @apply flex flex-col overflow-y-auto transition-all duration-300 ease-linear mt-auto;
}
.sidebar.dark-theme .sidebar-footer {
  @apply border-gray-700;
}
.drawer-title {
  @apply font-semibold text-custom-xl flex items-center;
}
.drawer-title-container {
  @apply flex items-center space-x-custom-3;
}
.drawer-title-icon {
  @apply w-5 h-5 text-custom-text-notification;
}
.drawer-title-name {
  @apply leading-none;
}
/* Sidebar Index end */
/* Sidebar Dropdown start */
.sidebar-dropdown {
  @apply mt-custom-2 mb-custom-2 flex flex-col gap-custom-1.5 pl-custom-9;
}
.sidebar-dropdown-item {
  @apply relative flex rounded-custom-lg px-custom-3.5 py-custom-2 font-medium transition-colors duration-300 ease-in-out;
}
.sidebar-dropdown-item-active {
  @apply text-primary-500 font-semibold;
}
.sidebar-dropdown-item-inactive {
  @apply text-custom-text-secondary hover:bg-custom-bg-muted hover:text-custom-text-primary;
  @apply hover:bg-[#F26A1B]/10;
}
.sidebar-footer-container {
  @apply flex flex-col items-center py-custom-4 relative gap-custom-4 font-medium overflow-visible;
}
.sidebar-footer-container-icon {
  @apply text-custom-text-notification transition-all duration-200 ease-in-out;
  @apply hover:text-[#F26A1B];
}
.sidebar-footer-container-logout-icon {
  @apply text-custom-text-notification group-hover:text-red-500 transition-colors duration-200 ease-in-out;
}
.sidebar-icon {
  @apply size-custom-7 text-custom-text-primary transition-all duration-200 ease-in-out stroke-[3px];
}
.logout-icon {
  @apply size-custom-7;
}
.drawer-title {
  @apply flex justify-between items-center;
}
/* Sidebar Footer end */
/* Sidebar Item start */
.sidebar-item {
  @apply mb-custom-xs flex justify-center;
}
.sidebar-item.dark-theme {
  @apply text-white;
}
/* Create the wrapper for the icon background but keep icon at original size - balanced spacing */
.sidebar-icon-wrapper {
  @apply flex items-center justify-center relative transition-all duration-200 ease-in-out rounded-xl h-11 w-11;
}
.sidebar-icon-wrapper.dark-theme {
  @apply bg-transparent;
}
.sidebar-link {
  @apply flex items-center justify-center gap-custom-2 font-medium relative transition-all duration-200 ease-in-out;
}
/* Responsive sidebar link padding */
@media (max-width: 1280px) {
  .sidebar-link {
    @apply p-3;
  }
}
@media (max-width: 1024px) {
  .sidebar-link {
    @apply p-2.5;
  }
}
.sidebar-link:not(.active) {
  @apply text-custom-text-primary hover:text-custom-text-secondary;
  @apply hover:text-[#F26A1B];
}
.sidebar-link.active .sidebar-icon-wrapper {
  @apply bg-[#F26A1B]/10;
}
/* Remove the vertical orange line on the left of active items */
.sidebar-link.active::before {
  display: none;
}
.sidebar-link:hover .sidebar-icon-wrapper:not(.active) {
  @apply bg-[#F26A1B]/10;
}
.sidebar-link.active {
  @apply text-[#F26A1B];
}
.sidebar.dark-theme .sidebar-link:not(.active) {
  @apply text-gray-200 hover:text-white;
}
.sidebar.dark-theme .sidebar-link.active {
  @apply text-[#F26A1B];
}
.sidebar.dark-theme .sidebar-link:hover .sidebar-icon-wrapper:not(.active) {
  @apply bg-[#F26A1B]/20;
}
.sidebar-icon.active {
  @apply text-[#f26a1b] fill-current;
}
.sidebar-icon:not(.active) {
  @apply text-custom-text-primary;
}
.sidebar.dark-theme .sidebar-icon:not(.active) {
  @apply text-gray-400;
}
.sidebar-icon-wrapper .sidebar-icon {
  @apply size-custom-6;
}
/* Responsive sidebar icon sizes */
@media (max-width: 1280px) {
  .sidebar-icon {
    @apply size-6;
  }
  .sidebar-icon-wrapper {
    @apply w-10 h-10;
  }
}
@media (max-width: 1024px) {
  .sidebar-icon {
    @apply size-5;
  }
  .sidebar-icon-wrapper {
    @apply w-9 h-9;
  }
}
.sidebar-dropdown-icon {
  @apply ml-auto size-custom-4 transform rotate-0 transition-transform duration-300 ease-in-out;
}
.sidebar-dropdown-icon.rotated {
  @apply rotate-180;
}
@keyframes rotate-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Animated buttons styling */
.plan-button::before {
  content: '';
  @apply absolute inset-[-2px] rounded-lg;
  background: conic-gradient(
    from 0deg at 50% 50%,
    #F26A1B,
    #ffb86c,
    #F26A1B,
    #ffb86c,
    #F26A1B
  );
  animation: rotate-border 3s linear infinite;
  z-index: 0;
}
.plan-button::after {
  content: '';
  @apply absolute inset-[2px] rounded-md;
  background: linear-gradient(to bottom, hsl(var(--primary-500)), hsl(var(--primary-600)));
  z-index: 1;
}
.plan-button img {
  @apply relative z-10;
}
@keyframes rotate-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.animated-border-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: inherit;
  background: conic-gradient(
    from 0deg,
    hsl(var(--primary-500)),
    hsl(var(--primary-300)),
    hsl(var(--primary-500)),
    hsl(var(--primary-300)),
    hsl(var(--primary-500))
  );
  animation: rotate-border 3s linear infinite;
  z-index: -1;
}
.animated-border-button::after {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 0.75rem;
  background: linear-gradient(to bottom, hsl(var(--primary-500)), hsl(var(--primary-700)));
  z-index: -1;
}
/* Sidebar Item end */