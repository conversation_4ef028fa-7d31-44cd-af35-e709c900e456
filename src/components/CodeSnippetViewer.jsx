import React, { useState } from 'react';
import { Check, Copy } from 'lucide-react';
import { PrismLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import {
  tomorrow,
  solarizedlight,
} from 'react-syntax-highlighter/dist/esm/styles/prism';

const CodeSnippetViewer = ({ code, language }) => {
  const [copied, setCopied] = useState(false);
  const [darkMode, setDarkMode] = useState(true);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <div className="rounded-lg border relative overflow-hidden">
      <div className={`px-4 py-2 flex justify-between items-center ${
        darkMode ? 'bg-gray-800' : 'bg-gray-100'
      }`}>
        <span className={`typography-body-sm font-weight-medium ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {language}
        </span>
        <button
          onClick={copyToClipboard}
          className={`${
            darkMode
              ? 'text-gray-400 hover:text-gray-200'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          {copied ? <Check size={18} /> : <Copy size={18} />}
        </button>
      </div>
      <div className="overflow-x-auto">
        <SyntaxHighlighter
          language={language?.toLowerCase()}
          style={darkMode ? tomorrow : solarizedlight}
          customStyle={{
            margin: 0,
            padding: '1rem',
            backgroundColor: darkMode ? '#1a202c' : '#f7fafc',
            fontSize: 'var(--font-size-code)',
            lineHeight: '1.5',
          }}
        >
          {code}
        </SyntaxHighlighter>
      </div>
      {copied && (
        <div className="mt-2 p-4 bg-green-100 border-l-4 border-green-500 text-green-700">
          <p className="typography-body-sm">Code copied to clipboard!</p>
        </div>
      )}
    </div>
  );
};

export default CodeSnippetViewer;