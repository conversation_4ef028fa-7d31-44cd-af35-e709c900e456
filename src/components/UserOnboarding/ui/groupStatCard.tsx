import { FC } from "react";

interface GroupStatCardProps {
    title: string;
    value: string | number;
    details?: string;
    type?: 'members' | 'active' | 'resources';
}

const GroupStatCard: FC<GroupStatCardProps> = ({title, value, details, type = 'members'}) => {
    const colorClasses = {
        members: 'text-primary',
        active: 'text-green-600',
        resources: 'text-purple-600'
    };

    return (
        <div className="p-4 shadow-[0_0_10px_rgba(0,0,0,0.1)] rounded-md flex flex-col gap-2 w-full">
            <div>
                <h2 className={`font-weight-bold typography-heading-2 ${colorClasses[type]}`}>{value}</h2>
                <p className={`typography-heading-4 ${colorClasses[type]}`}>{title}</p>
            </div>
            <p className="text-gray-500">{details}</p>
        </div>
    );
};

export default GroupStatCard;