import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSearchParams, useParams } from 'next/navigation';
import { mergeChanges, downloadFigmaCode } from '@/utils/FigmaAPI';
import { Download, ArrowLeftRight } from "lucide-react";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

/**
 * Self-contained button component for downloading and merging design changes
 * Retrieves all required parameters from URL/router
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isDisabled - Whether the buttons should be disabled
 */
const MergeButtonSection = ({ isDisabled = false }) => {
  const [merging, setMerging] = useState(false);
  const [mergeStatus, setMergeStatus] = useState(null);
  const [downloading, setDownloading] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState(null);
  
  // Get parameters directly from URL/router
  const searchParams = useSearchParams();
  const params = useParams();
  const figmaDiscussionId = searchParams.get('figmaDiscussionId');
  const selectedDesignId = searchParams.get("selectedDesignId");
  const projectId = params.projectId;
  const designType = searchParams.get("type");

  // Auto-hide status message utility
  const autoHideStatus = useCallback((setStatusFn) => {
    setTimeout(() => setStatusFn(null), 3000);
  }, []);

  // Handle the download code action
  const handleDownloadCode = useCallback(async () => {
    if (!figmaDiscussionId) {
      setDownloadStatus({
        success: false,
        message: "No discussion ID available for download"
      });
      autoHideStatus(setDownloadStatus);
      return;
    }

    setDownloading(true);
    try {
      await downloadFigmaCode(figmaDiscussionId);
      setDownloadStatus({
        success: true,
        message: "Download started successfully"
      });
    } catch (error) {
      setDownloadStatus({
        success: false,
        message: error.message || "Failed to download code"
      });
    } finally {
      setDownloading(false);
      autoHideStatus(setDownloadStatus);
    }
  }, [figmaDiscussionId, autoHideStatus]);

  // Handle the merge changes action
  const handleMergeChanges = useCallback(async () => {
    if (!projectId || !figmaDiscussionId || !selectedDesignId) {
      setMergeStatus({
        success: false,
        message: "Missing required parameters for merge"
      });
      autoHideStatus(setMergeStatus);
      return;
    }

    setMerging(true);
    try {
      await mergeChanges(projectId, figmaDiscussionId, selectedDesignId, designType);
      setMergeStatus({
        success: true,
        message: "Changes merged successfully"
      });
    } catch (error) {
      setMergeStatus({
        success: false,
        message: error.message || "Failed to merge changes"
      });
    } finally {
      setMerging(false);
      autoHideStatus(setMergeStatus);
    }
  }, [projectId, figmaDiscussionId, selectedDesignId, designType, autoHideStatus]);

  const StatusMessage = ({ status }) => (
    <div 
      className={`typography-caption px-2 py-1 rounded font-weight-medium shadow-sm absolute top-14 right-4 z-50 ${
        status?.success
          ? 'bg-green-50 text-green-700 border border-green-200' 
          : 'bg-red-50 text-red-700 border border-red-200'
      }`}
      role="status"
      aria-live="polite"
    >
      <span className="flex items-center">
        {status?.success ? (
          <svg className="w-3 h-3 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        ) : (
          <svg className="w-3 h-3 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        )}
        {status?.message}
      </span>
    </div>
  );

  const LoadingSpinner = () => (
    <svg className="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
    </svg>
  );

  return (
    <div className="flex items-center gap-2">
      {/* Status messages */}
      {(mergeStatus || downloadStatus) && (
        <StatusMessage status={mergeStatus || downloadStatus} />
      )}

      {/* Download Button */}
      <BootstrapTooltip title="Download code" placement="bottom">
        <button
          onClick={handleDownloadCode}
          disabled={downloading || isDisabled}
          className="flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors group disabled:opacity-50 disabled:cursor-not-allowed"
          aria-busy={downloading}
        >
          {downloading ? (
            <LoadingSpinner />
          ) : (
            <Download className="w-3 h-3 opacity-100 transition-opacity duration-200" />
          )}
          <span className={downloading ? "block" : "group-hover:hidden"}>
            {downloading ? "Downloading..." : ""}
          </span>
          {!downloading && (
            <span className="hidden group-hover:inline">Download Code</span>
          )}
        </button>
      </BootstrapTooltip>

      {/* Merge Button */}
      <BootstrapTooltip title="Merge changes" placement="bottom">
        <button
          onClick={handleMergeChanges}
          disabled={merging || isDisabled}
          className="flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors group disabled:opacity-50 disabled:cursor-not-allowed"
          aria-busy={merging}
        >
          {merging ? (
            <LoadingSpinner />
          ) : (
            <ArrowLeftRight className="w-3 h-3 opacity-100 transition-opacity duration-200" />
          )}
          <span className={merging ? "block" : "group-hover:hidden"}>
            {merging ? "Merging..." : ""}
          </span>
          {!merging && (
            <span className="hidden group-hover:inline">Merge Changes</span>
          )}
        </button>
      </BootstrapTooltip>
    </div>
  );
};

MergeButtonSection.propTypes = {
  isDisabled: PropTypes.bool
};

export default MergeButtonSection;