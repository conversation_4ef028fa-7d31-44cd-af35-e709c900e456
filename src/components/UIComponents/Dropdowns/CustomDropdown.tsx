import React, { useEffect, useRef, useState } from 'react';
import { MoreHorizontal } from 'lucide-react';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';

export interface MenuOption {
  label: string;
  icon: React.FC<{ className?: string }>;
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
  variant?: 'default' | 'danger';
  tooltip?: string;
}

interface CustomDropdownProps {
  options: MenuOption[];
  isLoading?: boolean;
  className?: string;
  align?: 'left' | 'right';
  size?: 'sqSmall' | 'sqDefault';
  onOpen?: () => void;  // Add this new prop
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  isLoading = false,
  className = '',
  align = 'right',
  size = 'sqDefault',
  onOpen,  // Add to props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    
    if (newIsOpen && onOpen) {
      onOpen();
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <DynamicButton
        variant="square"
        size={size}
        icon={MoreHorizontal}
        onClick={handleToggle}
        disabled={isLoading}
        aria-expanded={isOpen}
        aria-haspopup="true"
        tooltip='Menu'
      >
        <span className="sr-only">Open menu</span>
      </DynamicButton>

      {isOpen && (
        <div
          className={`absolute z-50 mt-1 ${align === 'right' ? 'right-0' : 'left-0'} 
            rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 
            focus:outline-none py-1`}
          role="menu"
          aria-orientation="vertical"
        >
          {options.map((option, index) => {
            const Icon = option.icon;
            return (
              <BootstrapTooltip title={option.tooltip} key={index} placement="left">
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  option.onClick(e);
                  setIsOpen(false);
                }}
                className={`
                  w-full text-left px-4 py-2 typography-body-sm flex items-center space-x-2
                  ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  ${option.variant === 'danger'
                    ? 'text-red-700 hover:bg-red-50'
                    : 'text-gray-700 hover:bg-gray-50'
                  }
                  transition-colors duration-150 ease-in-out
                `}
                disabled={option.disabled}
                role="menuitem"
              >
                <Icon className="h-4 w-4 flex-shrink-0 mr-2" />
                <span className='whitespace-nowrap'>{option.label}</span>
              </button>
              </BootstrapTooltip>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default CustomDropdown;

