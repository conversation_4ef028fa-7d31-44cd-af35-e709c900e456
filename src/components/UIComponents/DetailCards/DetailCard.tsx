import React, { useRef, useState } from 'react';
import {
    ChevronDown, Square, Calendar,
    Users, Clock, CheckCircle, Eye,
    ListTodo, AlertTriangle, Flame, Activity, LeafyGreen,
    Siren, Flag, FileText, SquarePlus, CircleDot,
    UserCog, Layers, User, LucideIcon,
    Timer
} from "lucide-react";
import { Accordion } from '@/components/UIComponents/Accordions/Accordion';
import { renderHTML } from '@/utils/helpers';
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import ListUserAssignModal from '@/components/Modal/ListUserAssignModal';

type Status = 'DONE' | 'IN PROGRESS' | 'IN REVIEW' | 'TODO' | 'N/A';
type Priority = 'Critical' | 'High' | 'Medium' | 'Low';
type NodeType = 'Epic' | 'UserStory' | 'Task';

interface StatusConfig {
    icon: LucideIcon;
    styles: string;
    iconColor: string;
}

interface PriorityConfig {
    icon: LucideIcon;
    styles: string;
    iconColor: string;
    animation?: string;
    pulse?: boolean;
}

interface NodeProperties {
    Status?: Status;
    Priority?: string | number;
    DueDate?: string;
    StoryPoints?: string;
    AssignedTo?: string;
    UserStoryType?: string;
    Description?: string;
}

interface NodeDetails {
    id?: string;
    title?: string;
    type?: NodeType;
    properties?: NodeProperties;
    assignee_name?: string;
    lastUpdated?: string;
}

interface StatusBadgeProps {
    status: Status;
}

interface PriorityBadgeProps {
    priority: string | number | undefined;
    isDropdown?: boolean;
}

interface HeadingWithIconProps {
    icon: LucideIcon;
    text: string;
}

interface NameAvatarProps {
    assigneeName?: string;
}

interface BadgeProps {
    children: React.ReactNode;
    className?: string;
}

interface UIMetadataField {
    Label: string;
    order: number;
    display_type: 'text' | 'number' | 'date' | 'rich_text';
    hidden?: boolean;
}

interface UIMetadata {
    [key: string]: UIMetadataField;
}

interface NodeProperties {
    Status?: Status;
    Priority?: string | number;
    DueDate?: string;
    StoryPoints?: string;
    AssignedTo?: string;
    UserStoryType?: string;
    Description?: string;
    Title?: string;
    Type?: string;
    configuration_state?: string;
    [key: string]: any; // For any additional properties
}

interface NodeDetails {
    id?: string;
    title?: string;
    type?: NodeType;
    labels?: string[]
    properties?: NodeProperties;
    assignee_name?: string;
    lastUpdated?: string;
    ui_metadata?: UIMetadata;
}

interface RequirementCardProps {
    nodeDetails: NodeDetails;
    onAssignUser?: (show: boolean) => void;
    updatePriority?: (priority: string, nodeId: string, event: React.MouseEvent) => void;
    listAllUsers?: { users: any[] };
    refresh?: boolean;
    setRefresh?: (refresh: boolean) => void;
    updateNodeDetails?: (details: any) => void;
    onKeyUpdate?: (key: string, value: string) => Promise<void>;

}



interface PrioritySelectProps {
    priority: string | number | undefined;
    updatePriority: (priority: string, nodeId: string, event: React.MouseEvent) => void;
    nodeId: string | undefined;
}

interface PropertyValueProps {
    propertyKey: string;
    value: any;
    metadata: UIMetadataField;
    onKeyUpdate?: (key: string, value: string) => Promise<void>;
    nodeDetails?: NodeDetails;
    updatePriority?: (priority: string, nodeId: string, event: React.MouseEvent) => void;
}

// Reusable components from the original file
const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
    const statusConfig: Record<Status, StatusConfig> = {
        'DONE': {
            icon: CheckCircle,
            styles: 'bg-green-100 text-green-800 border-green-200',
            iconColor: 'text-green-600'
        },
        'IN PROGRESS': {
            icon: Clock,
            styles: 'bg-yellow-100 text-yellow-800 border-yellow-200',
            iconColor: 'text-yellow-600'
        },
        'IN REVIEW': {
            icon: Eye,
            styles: 'bg-primary-100 text-primary-800 border-primary-200',
            iconColor: 'text-primary-600'
        },
        'TODO': {
            icon: ListTodo,
            styles: 'bg-gray-100 text-gray-800 border-gray-200',
            iconColor: 'text-gray-600'
        },
        'N/A': {
            icon: AlertTriangle,
            styles: 'bg-gray-100 text-gray-600 border-gray-200',
            iconColor: 'text-gray-500'
        }
    };

    const config = statusConfig[status || 'N/A'];
    const Icon = config.icon;

    return (
        <span className={`inline-flex items-center px-1.5 sm:px-2 py-1 typography-caption font-weight-medium rounded-lg border min-w-0 max-w-full ${config.styles}`}>
            <Icon className={`w-3 h-3 mr-1.5 flex-shrink-0 ${config.iconColor}`} />
            <span className="truncate">{status || 'N/A'}</span>
        </span>
    );
};

const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, isDropdown = false }) => {
    const normalizedPriority = (() => {
        if (typeof priority?.toString() === "number") return 'Low';
        return priority;
    })();

    const priorityConfig: Record<Priority | 'default', PriorityConfig> = {
        'Critical': {
            icon: Siren,
            styles: 'bg-red-100 text-red-900 border-red-300 shadow-md ring-2 ring-red-400 ring-opacity-50',
            iconColor: 'text-red-700',
            animation: 'animate-pulse'
        },
        'High': {
            icon: Flame,
            styles: 'bg-red-100 text-red-800 border-red-200 shadow-sm',
            iconColor: 'text-red-600',
            pulse: true
        },
        'Medium': {
            icon: Activity,
            styles: 'bg-yellow-100 text-yellow-800 border-yellow-200 shadow-sm',
            iconColor: 'text-yellow-600',
            animation: 'hover:scale-105 transition-transform duration-200'
        },
        'Low': {
            icon: LeafyGreen,
            styles: 'bg-green-100 text-green-800 border-green-200 shadow-sm',
            iconColor: 'text-green-600'
        },
        'default': {
            icon: AlertTriangle,
            styles: 'bg-gray-100 text-gray-600 border-gray-200 shadow-sm',
            iconColor: 'text-gray-500'
        }
    };

    const config = priorityConfig[normalizedPriority as Priority] || priorityConfig.default;
    const Icon = config.icon;

    return (
        <span
            className={`
        inline-flex items-center px-2 py-1 typography-caption font-weight-medium
        rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
        ${config.animation || ''} cursor-default
      `}
        >
            <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
            {normalizedPriority || 'Not Set'}
            {isDropdown && <ChevronDown className="w-3 h-3 ml-1" />}
        </span>
    );
};

const HeadingWithIcon: React.FC<HeadingWithIconProps> = ({ icon: Icon, text }) => (
    <div className="flex items-center gap-2 typography-body-sm font-weight-medium text-gray-500 mb-2">
        <Icon className="w-4 h-4" />
        <h3>{text}</h3>
    </div>
);


const PropertyValue: React.FC<PropertyValueProps> = ({
    propertyKey,
    value,
    metadata,
    onKeyUpdate,
    nodeDetails,
    updatePriority
}) => {
    const [isEditing, setIsEditing] = useState(false);
    const [localValue, setLocalValue] = useState(value);
    const inputRef = useRef<HTMLInputElement>(null);

    const handleStartEditing = () => {
        if (onKeyUpdate) {
            setIsEditing(true);
            setLocalValue(value);
            setTimeout(() => {
                inputRef.current?.focus();
            }, 0);
        }
    };

    const handleSaveEdit = async () => {
        if (onKeyUpdate) {
            let isValid = true;
            let processedValue = localValue;

            switch (metadata?.display_type) {
                case 'number':
                    const numericValue = localValue.replace(/[^0-9]/g, '');
                    processedValue = numericValue.slice(0, 2);
                    isValid = processedValue.length > 0;
                    break;
                case 'text':
                    const words = localValue.trim().split(/\s+/);
                    isValid = words.length <= 2;
                    processedValue = words.slice(0, 2).join(' ');
                    break;
            }

            if (isValid) {
                await onKeyUpdate(propertyKey, processedValue);
                setIsEditing(false);
            } else {
                inputRef.current?.classList.add('border-red-500');
                setTimeout(() => {
                    inputRef.current?.classList.remove('border-red-500');
                }, 1000);
            }
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let inputValue = e.target.value;

        switch (metadata?.display_type) {
            case 'number':
                inputValue = inputValue.replace(/[^0-9]/g, '').slice(0, 2);
                break;
            case 'text':
                // Additional text validation if needed
                break;
        }

        setLocalValue(inputValue);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleSaveEdit();
        } else if (e.key === 'Escape') {
            setIsEditing(false);
            setLocalValue(value);
        }
    };

    if (!value || !metadata) return null;

    if (metadata.display_type === 'rich_text') {
        return null;
    }

    if (propertyKey === 'Priority') {
        return updatePriority ? (
            <PrioritySelect
                priority={value}
                updatePriority={updatePriority}
                nodeId={nodeDetails?.id}
            />
        ) : (
            <PriorityBadge priority={value} />
        );
    }

    if (propertyKey === 'AssignedTo') {
        return (
            <div className="flex items-center gap-2">
                <NameAvatar assigneeName={value} />
            </div>
        );
    }

    switch (metadata.display_type) {
        case 'number':
            if (onKeyUpdate) {
                return isEditing ? (
                    <input
                        ref={inputRef}
                        type="text"
                        value={localValue}
                        onChange={handleInputChange}
                        onBlur={handleSaveEdit}
                        onKeyDown={handleKeyDown}
                        className="typography-body-sm text-gray-700 rounded-lg border-b border-blue-500 focus:outline-none"
                        maxLength={2}
                    />
                ) : (
                    <span
                        onClick={handleStartEditing}
                        className="hover:bg-gray-100 transition-colors"
                    >
                        <Badge>{value}</Badge>
                    </span>
                );
            }
            return <Badge>{value}</Badge>;
        case 'date':
            return <Badge>{value}</Badge>;
        case 'text':
            if (onKeyUpdate) {
                return isEditing ? (
                    <input
                        ref={inputRef}
                        type="text"
                        value={localValue}
                        onChange={handleInputChange}
                        onBlur={handleSaveEdit}
                        onKeyDown={handleKeyDown}
                        className="typography-body-sm text-gray-700 rounded-lg border-b border-blue-500 focus:outline-none"
                    />
                ) : (
                    <span
                        onClick={handleStartEditing}
                        className="hover:bg-gray-100 transition-colors"
                    >
                        <Badge>{value}</Badge>
                    </span>
                );
            }
            return <Badge>{value}</Badge>;
        default:
            return <Badge>{value}</Badge>;
    }
};


const NameAvatar: React.FC<NameAvatarProps> = ({ assigneeName }) => {
    const getInitials = (name: string): string => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <div className="flex items-center gap-2">
            <div className="relative inline-flex items-center justify-center w-6 h-6 overflow-hidden rounded-md border border-[#D2D5DC80] bg-[#EBF5FF]">
                {
                    assigneeName ? (
                        <span className="font-weight-medium typography-caption text-[#1C64F2]">
                            {getInitials(assigneeName)}
                        </span>
                    )
                        :
                        (
                            <User className='w-3 h-3 text-[#1C64F2]' />
                        )
                }
            </div>
            <span className="whitespace-nowrap text-gray-500">{assigneeName || "Unassigned"}</span>
        </div>
    );
};

const Badge: React.FC<BadgeProps> = ({ children, className = '' }) => {
    return (
        <span className={`
            inline-flex items-center
            rounded-md
            px-2 py-0.5
            typography-caption
            font-weight-medium
            bg-gray-100
            text-gray-700
            border
            border-gray-300
            ${className}
        `}>
            {children}
        </span>
    );
};

// Additional supporting component for Priority Selection
const PrioritySelect: React.FC<PrioritySelectProps> = ({ priority, updatePriority, nodeId }) => {
    const [isOpen, setIsOpen] = useState(false);
    const priorityOptions: Priority[] = ['Low', 'Medium', 'High', 'Critical'];

    const currentPriority = typeof priority === "number" || (!isNaN(Number(priority)) && typeof priority === "string")
        ? "Low"
        : priority || "Low";

    const handleChevronDownClick = (e: React.MouseEvent): void => {
        e.stopPropagation();
        setIsOpen(!isOpen);
    };

    const handlePriorityUpdate = (selectedPriority: Priority, e: React.MouseEvent): void => {
        updatePriority(selectedPriority, nodeId || '', e);
        setIsOpen(false);
    };

    return (
        <div className="relative inline-block">
            <button
                onClick={handleChevronDownClick}
                className={`
          inline-flex items-center gap-1.5 rounded-full px-2 py-1 typography-caption font-weight-medium
          hover:bg-opacity-80 transition-colors duration-200
        `}
            >
                <PriorityBadge priority={currentPriority} isDropdown={true} />
            </button>

            {isOpen && (
                <div className="absolute z-10 mt-1 bg-white rounded-lg border shadow-lg py-1">
                    {priorityOptions.map((priorityOption, idx) => (
                        <button
                            key={idx}
                            onClick={(e) => handlePriorityUpdate(priorityOption, e)}
                            className={`
                w-full text-left px-3 py-1.5 typography-body-sm
                hover:bg-gray-50 transition-colors duration-200 inline-flex items-center justify-center
              `}
                        >
                            <PriorityBadge priority={priorityOption} />
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

const shouldUseAccordion = (content: string): boolean => {
    if (!content) return false;
    const lineCount = content.split("\n").length;
    const wordCount = content.split(/\s+/).length;
    return lineCount > 10 || wordCount > 200;
};

const RequirementCard: React.FC<RequirementCardProps> = ({
    nodeDetails,
    onAssignUser,
    updatePriority,
    listAllUsers,
    refresh,
    setRefresh,
    updateNodeDetails,
    onKeyUpdate,
}) => {
    const [showUsersToAssign, setShowUsersToAssign] = useState(false);

    const typeBadgeColors = {
        Epic: "bg-purple-100 text-purple-700 max-w-14",
        UserStory: "bg-green-100 text-green-700 max-w-24",
        Task: "bg-blue-100 text-blue-700 max-w-14",
        default: "bg-white max-w-20",
    };

    // Icon mapping for property fields
    const iconMapping: Record<string, any> = {
        Priority: Flag,
        DueDate: Calendar,
        StoryPoints: CircleDot,
        AssignedTo: UserCog,
        UserStoryType: Layers,
        EstimatedDuration: Timer,
    };

    // Skip these properties in the loop
    const skipProperties = ['Title', 'Type', 'Status', 'configuration_state', 'Assignee'];

    // Function to render property value based on metadata type
    const renderProperties = () => {
        const properties = nodeDetails?.properties;
        const metadata = nodeDetails?.ui_metadata;

        if (!properties || !metadata) return null;

        return Object.entries(properties)
            .filter(([key]) => !skipProperties.includes(key))
            .sort((a, b) => {
                const orderA = metadata[a[0]]?.order ?? 999;
                const orderB = metadata[b[0]]?.order ?? 999;
                return orderA - orderB;
            })
            .map(([key, value]) => {
                const fieldMetadata = metadata[key];
                if (fieldMetadata?.hidden) return null;

                const IconComponent = iconMapping[key] || Layers;

                // Use fallback for missing value
                const displayValue = value || 'N/A';

                if (fieldMetadata?.display_type === 'rich_text') {
                    return null;
                }


                return (
                    <div className="flex flex-col items-start space-y-1" key={key}>
                        <HeadingWithIcon
                            icon={IconComponent}
                            text={fieldMetadata?.Label || key}
                        />
                        <PropertyValue
                            propertyKey={key}
                            value={displayValue}
                            metadata={fieldMetadata}
                            onKeyUpdate={onKeyUpdate}
                            nodeDetails={nodeDetails}
                            updatePriority={updatePriority}
                        />
                    </div>
                );
            });
    };



    return (
        <div className="relative mx-auto mt-3 bg-white rounded-lg border shadow">
            <div className="p-6 space-y-4">
                {/* Header section */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="absolute top-[-8px] left-[1%]">
                            {/* SVG Bookmark Icon */}
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 68" className="w-6">
                                <path
                                    d="M2 2 L22 2 L22 60 L12 44 L2 60 Z"
                                    fill="#3B82F6"
                                    stroke="#2563EB"
                                    strokeWidth="2"
                                    className="transition-all duration-300 ease-in-out hover:fill-blue-700"
                                />
                                <path
                                    d="M2 2 L2 60 L12 44 L22 60 L22 2"
                                    fill="none"
                                    stroke="#1E40AF"
                                    strokeWidth="1"
                                    className="opacity-50"
                                />
                            </svg>
                        </div>
                        <h2 className="typography-body font-weight-semibold text-gray-900 pl-4">
                            {nodeDetails?.title || nodeDetails?.properties?.Title || 'Untitled'}
                        </h2>
                    </div>
                    <div className="flex items-center gap-2">
                        {/* <StatusBadge status={nodeDetails?.properties?.Status || 'N/A'} /> */}
                        {nodeDetails?.type && (<span className={`inline-flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium rounded-md ${typeBadgeColors[nodeDetails?.type as keyof typeof typeBadgeColors] || typeBadgeColors.default}`}>
                            <Square fill="currentColor" className="w-2 h-2" />
                            {nodeDetails?.type}
                        </span>)}
                    </div>
                </div>

                {/* Properties grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 border-t border-b py-4">
                    {/* Assignee section - always shown */}
                    {
                        nodeDetails?.labels?.some(label => label.toLowerCase() === 'workitem') ? (
                            <div className="flex flex-col items-start space-y-1">
                                <HeadingWithIcon icon={Users} text="Assignees" />
                                <div className="flex items-center gap-2">
                                    {nodeDetails?.properties?.Assignee ? (
                                        <NameAvatar assigneeName={nodeDetails?.properties?.Assignee} />
                                    ) : (
                                        <>
                                            {listAllUsers?.users?.length ? (
                                                <>
                                                    <ListUserAssignModal
                                                        users={listAllUsers?.users ?? []}
                                                        show={showUsersToAssign}
                                                        setShow={setShowUsersToAssign}
                                                        nodeid={nodeDetails?.id}
                                                        refresh={refresh}
                                                        setRefresh={setRefresh}
                                                        updateNodeDetails={updateNodeDetails}
                                                    />
                                                    <IconButton
                                                        icon={<SquarePlus className="h-4 w-4 text-gray-600" />}
                                                        tooltip="Assign user"
                                                        onClick={() => setShowUsersToAssign(true)}
                                                        className="hover:bg-gray-100"
                                                        variant="small"
                                                    />
                                                </>
                                            ) : (
                                                <span className="text-gray-500">N/A</span>
                                            )}
                                        </>
                                    )}
                                </div>
                            </div>
                        ) : null
                    }



                    {/* Dynamic properties */}
                    {renderProperties()}
                </div>

                {/* Description section */}
                <div className="border-b">
                    <Accordion
                        title="Description"
                        defaultOpen={true}
                        preview="Open this for Description"
                        icon={<FileText className="h-5 w-5" />}
                        mode={shouldUseAccordion(nodeDetails.properties?.Description || "No description provided") ? 'dynamic' : 'static'}
                    >
                        {nodeDetails?.properties?.Description
                            ? <span dangerouslySetInnerHTML={{ __html: renderHTML(nodeDetails.properties.Description) }}></span>
                            : "No description provided"
                        }
                    </Accordion>
                </div>

                {/* Footer */}
                <div className="typography-body-sm text-gray-500">
                    Created by {'Kavia AI'} • Last updated {nodeDetails?.lastUpdated || 'Never'}
                </div>
            </div>
        </div>
    );
};

export default RequirementCard;