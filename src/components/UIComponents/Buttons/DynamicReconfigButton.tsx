import React from 'react';
import { Loader2 } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';

// Define button variants
const BUTTON_VARIANTS = {
  primary: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 disabled:bg-primary-300',
  primaryLegacy: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 disabled:bg-primary-300',
  primaryOutline: 'bg-white text-gray-500 border border-gray-300/50 hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500',
  orange: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 disabled:bg-primary-300',
  orangeOutline: 'bg-white text-primary-500 border border-primary-300/50 hover:bg-primary-50 hover:border-primary-400 focus:ring-primary-500',
  purple: 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-gray-300 disabled:bg-gray-300',
  green: 'bg-green-600 text-white hover:bg-green-700 focus:ring-gray-300 disabled:bg-gray-300',
  secondary: 'bg-white border border-gray-200/50 text-gray-700 hover:bg-gray-200 focus:ring-gray-300 disabled:bg-gray-50',
  square: 'bg-white border border-gray-200/50 text-gray-700 hover:bg-gray-200 disabled:bg-gray-50',
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-gray-300 disabled:bg-gray-300',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-gray-300 disabled:bg-gray-300',
  ghost: 'text-gray-500 hover:text-gray-700 hover:bg-gray-200 focus:ring-gray-300',
  ghostPrimary: 'text-custom-text-primary hover:bg-gray-200',
  ghostDanger: 'text-custom-text-destructive hover:bg-gray-200',
  linkPrimary: 'text-gray-500 hover:bg-gray-200',
  linkDanger: 'text-red-600 hover:bg-gray-200',
  linkDisable: 'text-gray-400 bg-gray-50'
} as const;

// Define button sizes
const BUTTON_SIZES = {
  xsmall: 'px-3 py-1 typography-caption',
  small: 'px-3 py-1.5 typography-body-sm',
  medium: 'px-4 py-1.5 typography-body-sm',
  default: 'px-4 py-2 typography-body-sm max-h-9',
  large: 'px-6 py-3 typography-body',
  sqSmall: 'p-1.5 typography-body-sm',
  sqDefault: 'p-2 typography-body-sm',
} as const;

type ButtonVariant = keyof typeof BUTTON_VARIANTS;
type ButtonSize = keyof typeof BUTTON_SIZES;

interface DynamicButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: LucideIcon;
  text?: string;
  tooltip?: string;
  isLoading?: boolean;
  fullWidth?: boolean;
  className?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  disable?: boolean;
  loading?: boolean;
  isBlynk? :boolean
}

export const DynamicReconfigButton: React.FC<DynamicButtonProps> = ({
  children,
  variant = 'primary',
  size = 'default',
  icon: Icon,
  text = '',
  tooltip,
  isLoading = false,
  disabled = false,
  onClick,
  className = '',
  type = 'button',
  fullWidth = false,
  placement = 'bottom-end',
  disable = false,loading,
  isBlynk = false,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center gap-2 font-weight-medium transition-all duration-300 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-sm hover:shadow';

  const buttonClasses = [
    baseStyles,
    BUTTON_VARIANTS[variant],
    BUTTON_SIZES[size],
    fullWidth ? 'w-full' : '',
    (disabled || isLoading || disable) ? 'cursor-not-allowed opacity-50' : '',
    className,
  ].filter(Boolean).join(' ');

  const getLoadingText = (text: React.ReactNode): React.ReactNode => {
    if (typeof text !== 'string') return text;
    return text
      .replace('Submit', 'Submitting...')
      .replace('Delete', 'Deleting...')
      .replace('Create', 'Creating...')
      .replace('Update', 'Updating...');
  };

  return (
    <BootstrapTooltip title={tooltip || ''} placement={placement}>
      <div className="relative inline-block">
      {isBlynk && (
          <span className="absolute -top-1 right-[-5px] transform w-2.5 h-2.5 bg-red-500 rounded-full animate-[pulse_1s_infinite] z-10"></span>
        )}
      <button
        type={type}
        onClick={onClick}
        disabled={disabled || loading || disable}
        className={buttonClasses}
        {...props}
      >

        {loading ? (
          <>
            <Loader2 className="size-4 animate-spin" />
            {text && <span>{text.replace("Create", "Creating...")}</span>}
          </>
        ) : (
          <>
            {Icon && <Icon className="size-4 flex-shrink-0" />}
            {text && <span>{text} </span>}
          </>
        )}
        {/* {text.trim().length !== 0 && <span className='whitespace-nowrap'>{text}</span>} */}
      </button>
      </div>
    </BootstrapTooltip>
  );
};