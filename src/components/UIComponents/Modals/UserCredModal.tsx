import React, { useState } from 'react';
import { X, Check, LucideIcon } from 'lucide-react';
import { useContext } from 'react';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';

interface SuccessModalProps {
  isOpen: boolean;
  title: string;
  message?: string;
  onClose: () => void;
  inviteUrl?: string;
  loginUrl?: string;
  icon?: LucideIcon;
  iconClassName?: string;
}

export const UserCredModal: React.FC<SuccessModalProps> = ({
  isOpen,
  title,
  message,
  onClose,
  inviteUrl,
  loginUrl,
  icon: Icon = Check,
  iconClassName = "w-8 h-8 text-green-500"
}) => {
  const { showAlert } = useContext(AlertContext);
  const [copiedInvite, setCopiedInvite] = useState(false);
  const [copiedLogin, setCopiedLogin] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  if (!isOpen) return null;

  const copyToClipboard = async (text: string, type: 'invite' | 'login') => {
    try {
      await navigator.clipboard.writeText(text || '');
      if (type === 'invite') {
        setCopiedInvite(true);
        setTimeout(() => setCopiedInvite(false), 2000);
      } else {
        setCopiedLogin(true);
        setTimeout(() => setCopiedLogin(false), 2000);
      }
    } catch (err) {
      showAlert("Failed to copy URL", "danger");
    }
  };

  const createCredentialsContent = () => {
    return `User Credentials Information

Set Password URL:
${inviteUrl}

Login URL (after setting password):
${loginUrl}

Note: Please keep this information secure and delete after use.
    `;
  };

  const downloadCredentials = () => {
    const content = createCredentialsContent();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'user_credentials.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const shareCredentials = async () => {
    try {
      setIsSharing(true);
      const content = createCredentialsContent();
      const file = new File([content], 'user_credentials.txt', { type: 'text/plain' });
      
      if (navigator.share) {
        await navigator.share({
          files: [file],
          title: 'User Credentials',
          text: 'User credentials information'
        });
      } 
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        showAlert("Failed to share credentials", "danger");
      }
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon className={iconClassName} />
          </div>
          <h3 className="typography-heading-4 font-weight-semibold text-gray-900">{title}</h3>
          {message && <p className="text-gray-500 mt-2">{message}</p>}
        </div>

        {(inviteUrl || loginUrl) && (
          <div className="space-y-4">
            {inviteUrl && (
              <div className="space-y-1">
                <label className="block typography-body-sm font-weight-medium text-gray-700">
                  Set Password URL
                </label>
                <div className="bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between p-3">
                    <code className="typography-body-sm text-gray-600 break-all flex-1 ">
                      {inviteUrl}
                    </code>
                    <div className="relative ml-3 flex-shrink-0">
                      <button
                        onClick={() => copyToClipboard(inviteUrl, 'invite')}
                        className="p-2 text-gray-500 hover:text-blue-600 transition-colors rounded-md hover:bg-gray-100"
                      >
                        {copiedInvite && (
                          <div className="absolute -top-8 right-0 bg-gray-800 text-white typography-caption py-1 px-2 rounded animate-fade-in">
                            Copied!
                          </div>
                        )}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {loginUrl && (
              <div className="space-y-1">
                <label className="block typography-body-sm font-weight-medium text-gray-700">
                  Login URL
                </label>
                <div className="bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between p-3">
                    <code className="typography-body-sm text-gray-600 break-all flex-1 ">
                      {loginUrl}
                    </code>
                    <div className="relative ml-3 flex-shrink-0">
                      <button
                        onClick={() => copyToClipboard(loginUrl, 'login')}
                        className="p-2 text-gray-500 hover:text-blue-600 transition-colors rounded-md hover:bg-gray-100"
                      >
                        {copiedLogin && (
                          <div className="absolute -top-8 right-0 bg-gray-800 text-white typography-caption py-1 px-2 rounded animate-fade-in">
                            Copied!
                          </div>
                        )}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col gap-4 mt-6">
              <div className="flex gap-2">
                <button
                  onClick={downloadCredentials}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download
                </button>
                <button
                  onClick={shareCredentials}
                  disabled={isSharing}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isSharing ? (
                    <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                  )}
                  Share
                </button>
              </div>
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}; 