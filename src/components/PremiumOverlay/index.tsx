import React, { useEffect, useState } from 'react';
import { Lock, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface PremiumOverlayProps {
  isCreditLimitExceeded?: boolean;
  isUnauthorized?: boolean;
  onClose?: () => void;
  allowClose?: boolean;
}

const PremiumOverlay: React.FC<PremiumOverlayProps> = ({
  isCreditLimitExceeded = false,
  isUnauthorized = false,
  onClose,
  allowClose = true
}) => {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setTimeout(() => setIsAnimating(true), 50);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  // Debug log to check props
  useEffect(() => {

  }, [isCreditLimitExceeded, isUnauthorized]);

  if (!isVisible) {
    return null;
  }

  const getTitle = () => {
    if (isUnauthorized) return "Access Denied";
    if (isCreditLimitExceeded) return "Credit Limit Reached!";
    return "Premium Feature";
  };

  const getMessage = () => {
    if (isUnauthorized) {
      return "You are not authorized to access this resource. Please contact your admin for proper permissions.";
    }
    if (isCreditLimitExceeded) {
      return "Your credit limit has been reached. Please contact your admin to upgrade your plan for continued access.";
    }
    return "You are currently using our Free Tier plan. Upgrade now to access this and other essential features.";
  };

  return (
    <div
      className={`fixed inset-0 flex flex-col items-center justify-center z-[9999] transition-all duration-300 ${isAnimating ? 'opacity-100' : 'opacity-0'}`}
      onClick={(e) => e.stopPropagation()}
      style={{ pointerEvents: 'all', position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div className="absolute inset-0 bg-black/80 backdrop-blur-lg" style={{ pointerEvents: 'all' }}>
        <div className="absolute -top-80 -right-40 w-1/2 h-96 rounded-full bg-gradient-to-br from-primary-600/60 to-primary-800/50 blur-3xl opacity-50"></div>
      </div>

      <div
        className={`relative rounded-xl border border-primary-400/20 bg-gradient-to-tr from-[#1C1C1C] from-70% to-primary-400/20 p-8 shadow-2xl max-w-md mx-auto my-8 text-center transition-all duration-500 ${isAnimating ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-8'}`}
      >
        <div className="relative">
          {allowClose && (
            <div className="absolute -top-4 -right-4">
              <button
                onClick={() => {
                  setIsAnimating(false);
                  setTimeout(() => {
                    setIsVisible(false);
                    if (onClose) onClose();
                  }, 300);
                }}
                className="bg-white/10 p-2 rounded-full opacity-70 ring-offset-background transition-opacity cursor-pointer hover:opacity-100 focus:outline-none focus:ring-none"
              >
                <X className="h-4 w-4 text-white" />
                <span className="sr-only">Close</span>
              </button>
            </div>
          )}

          <div className="p-1 bg-primary-400/20 rounded-lg w-16 h-16 flex items-center justify-center mx-auto mb-4 transform transition-transform duration-300 hover:scale-105">
            <div className="bg-gradient-to-r from-primary-500 to-primary-400 w-full h-full rounded-lg flex items-center justify-center shadow-lg">
              <Lock className="w-8 h-8 text-white" />
            </div>
          </div>

          <h3 className="text-white typography-heading-4 font-weight-bold mb-2">
            {getTitle()}
          </h3>

          <p className="text-gray-300 mb-4 typography-body-sm leading-relaxed">
            {getMessage()}
          </p>

          {!isCreditLimitExceeded && !isUnauthorized && (
            <button
              onClick={() => router.push('/pricing')}
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
              className={`w-full bg-[#F15A24] hover:bg-[#d94d1a] text-white px-4 py-2 typography-body-sm rounded-lg transition-all duration-300 font-weight-medium transform ${isHovering ? 'shadow-xl -translate-y-1' : 'shadow-md'}`}
            >
              Upgrade Now
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PremiumOverlay;
