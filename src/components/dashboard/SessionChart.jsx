// components/dashboard/SessionChart.jsx
import React, {  useRef } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

const SessionChart = ({ chartData, loading, error, granularity = 'day' }) => {
  const chartRef = useRef(null);

  // Prepare chart data with better formatting
  const prepareChartData = () => {
    if (!chartData || !Array.isArray(chartData)) {
      return {
        labels: [],
        datasets: []
      };
    }

    // Format labels based on granularity
    const labels = chartData.map(item => {
      try {
        const date = new Date(item.date);
        if (granularity === 'hour') {
          return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        } else {
          return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            year: 'numeric'
          });
        }
      } catch (e) {
        return item.date;
      }
    });
    
    return {
      labels,
      datasets: [
        {
          label: 'Total Sessions',
          data: chartData.map(item => item.total_sessions || 0),
          backgroundColor: 'rgba(59, 130, 246, 0.7)',
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
        },
        {
          label: 'Unique Users',
          data: chartData.map(item => item.unique_users_count || 0),
          backgroundColor: 'rgba(16, 185, 129, 0.7)',
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
        },
        {
          label: 'Active Sessions',
          data: chartData.map(item => item.active_sessions || 0),
          backgroundColor: 'rgba(34, 197, 94, 0.7)',
          borderColor: 'rgb(34, 197, 94)',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
        },
        {
          label: 'Completed Sessions',
          data: chartData.map(item => item.completed_sessions || 0),
          backgroundColor: 'rgba(99, 102, 241, 0.7)',
          borderColor: 'rgb(99, 102, 241)',
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
        }
      ]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: `Session Activity Over Time (${granularity === 'day' ? 'Daily' : 'Hourly'})`,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      legend: {
        position: 'top',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          title: function(context) {
            return `${granularity === 'day' ? 'Date' : 'Time'}: ${context[0].label}`;
          },
          afterBody: function(context) {
            const dataIndex = context[0].dataIndex;
            const data = chartData[dataIndex];
            if (data) {
              return [
                `Total Cost: $${(data.total_cost || 0).toFixed(4)}`,
                `Unique Tenants: ${data.unique_tenants_count || 0}`,
                `Failed Sessions: ${data.failed_sessions || 0}`
              ];
            }
            return [];
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: granularity === 'day' ? 'Date' : 'Date & Time',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0,
          font: {
            size: 10
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Count',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        ticks: {
          precision: 0,
          font: {
            size: 10
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    }
  };

  // Loading skeleton
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-80 bg-gray-100 rounded flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
              <span className="text-gray-500">Loading chart data...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Chart</h3>
          <p className="text-sm text-gray-500 mb-4">
            {error?.message || 'There was an error loading the chart data.'}
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (!chartData || chartData.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📈</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
          <p className="text-sm text-gray-500">
            No session data found for the selected time period. Try adjusting your date range or clearing filters.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div style={{ height: '400px' }}>
        <Bar ref={chartRef} data={prepareChartData()} options={chartOptions} />
      </div>
      
      {/* Summary stats below chart */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {chartData.reduce((sum, item) => sum + (item.total_sessions || 0), 0)}
          </div>
          <div className="text-sm text-gray-500">Total Sessions</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {Math.max(...chartData.map(item => item.unique_users_count || 0), 0)}
          </div>
          <div className="text-sm text-gray-500">Peak Users</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {chartData.reduce((acc, item) => {
              return Math.max(acc, item.unique_tenants_count || 0);
            }, 0)}
          </div>
          <div className="text-sm text-gray-500">Peak Tenants</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">
            ${chartData.reduce((sum, item) => sum + (item.total_cost || 0), 0).toFixed(2)}
          </div>
          <div className="text-sm text-gray-500">Total Cost</div>
        </div>
      </div>
    </div>
  );
};

export default SessionChart;