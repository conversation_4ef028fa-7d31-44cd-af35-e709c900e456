// src/components/Graph3d.js
import React, { useEffect, useState, useRef } from 'react';
import ForceGraph3D from 'react-force-graph-3d';
import SpriteText from 'three-spritetext'; // Updated import
const NODE_R = 5; // Node radius
const Graph3d = ({ graphData, onNodeClick = () => {}, networkRef }) => {
  const [graphDatas, setGraphData] = useState({ nodes: [], links: [] });
  const [highlightNodes, setHighlightNodes] = useState(new Set());
  const [highlightLinks, setHighlightLinks] = useState(new Set());
  const [hoverNode, setHoverNode] = useState(null);
  const fgRef = useRef();
  const [initialZoomDone, setInitialZoomDone] = useState(false);
  
  useEffect(() => {
    setGraphData(graphData.data); // Directly access graphData's data
  }, [graphData]);

  useEffect(() => {
    if (fgRef.current && !initialZoomDone) {
      const fg = fgRef.current;
      fg.zoomToFit(400, 0);
      setInitialZoomDone(true);
    }
  }, [graphDatas, initialZoomDone]);
  useEffect(() => {
    if (fgRef.current) {
      const fg = fgRef.current;
      if (networkRef) {
        networkRef.current = {
          zoomIn: () => {
            const camera = fg.camera();
            const target = fg.controls().target;
            const distance = camera.position.distanceTo(target);
            const newDistance = distance * 0.8; // Zoom in by reducing distance
            const direction = camera.position.clone().sub(target).normalize();
            const newPosition = target.clone().add(direction.multiplyScalar(newDistance));
            fg.cameraPosition(newPosition, target, 800);
          },
          zoomOut: () => {
            const camera = fg.camera();
            const target = fg.controls().target;
            const distance = camera.position.distanceTo(target);
            const newDistance = distance * 1.2; // Zoom out by increasing distance
            const direction = camera.position.clone().sub(target).normalize();
            const newPosition = target.clone().add(direction.multiplyScalar(newDistance));
            fg.cameraPosition(newPosition, target, 800);
          },
          reset: () => {
            fg.zoomToFit(400);
          },
          getScale: () => {
            return fg.camera().position.distanceTo(fg.controls().target);
          },
          moveTo: ({ scale }) => {
            const currentPosition = fg.camera().position;
            const target = fg.controls().target;
            const direction = new THREE.Vector3().subVectors(currentPosition, target).normalize();
            const newPosition = target.clone().add(direction.multiplyScalar(scale));
            fg.cameraPosition(newPosition, target, 800);
          }
        };
      }
    }
  }, [networkRef]);
  const handleNodeClick = (node) => {
    const distance = 40;
    const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z);
    fgRef.current.cameraPosition(
      { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio },
      node,
      3000
    );
    if (node) {
      if (typeof onNodeClick === 'function') {
        onNodeClick(node); // Safely call the function
      }
    }
  };
  const handleNodeHover = (node) => {
    const newHighlightNodes = new Set();
    const newHighlightLinks = new Set();
    if (node) {
      newHighlightNodes.add(node);
      node.neighbors.forEach(neighbor => newHighlightNodes.add(neighbor));
      node.links.forEach(link => newHighlightLinks.add(link));
    }
    setHoverNode(node || null);
    setHighlightNodes(newHighlightNodes);
    setHighlightLinks(newHighlightLinks);
  };
  return (
    <div className='fixed'>
      <ForceGraph3D
        ref={fgRef}
        graphData={graphDatas}
        onNodeClick={handleNodeClick}
        nodeLabel={(node) => `<span style="color: black;">${node.label}</span>`}
        linkLabel={(node) => `<span style="color: black;">${node.type}</span>`}
        linkColor = "type"
        width={910}
        height={600}
        backgroundColor="rgba(0, 0, 0, 0)" 
        nodeAutoColorBy="type"
        onNodeHover={handleNodeHover}
        linkDirectionalArrowLength={3.5}
        linkDirectionalArrowRelPos={1}
        // linkWidth={link => (highlightLinks.has(link) ? 3 : 0.5)}
        linkThreeObjectExtend={true}
        linkThreeObject={link => {
          const sprite = new SpriteText(`${link.type}`);
          sprite.color = 'black';
          sprite.textHeight = 1.5;
          return sprite;
        }}
        linkPositionUpdate={(sprite, { start, end }) => {
          const middlePos = Object.assign(...['x', 'y', 'z'].map(c => ({
            [c]: start[c] + (end[c] - start[c]) / 2 // Calculate middle point
          })));
          // Position sprite
          Object.assign(sprite.position, middlePos);
        }}
        // cooldownTicks={100}
        // onEngineStop={() => fgRef.current.zoomToFit(300)}
      />
    </div>
  );
};
export default Graph3d;