import { useState, useRef, useCallback, useEffect } from 'react';

const AUTO_SCROLL_RESTORATION_DELAY = 5000; // 5 seconds before auto-scroll is restored

export const useScrollManagement = (messagesContainerRef, setAutoScroll, setHasNewMessages) => {
  const userManuallyScrolledRef = useRef(false);
  const isScrollingRef = useRef(false);
  const userScrollTimestampRef = useRef(Date.now());

  // Check if scroll position is at bottom
  const isAtBottom = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container) return true;

    // More strict threshold - only consider "at bottom" if very close to bottom
    const threshold = 50; // pixels from bottom to consider "at bottom"
    return container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
  }, [messagesContainerRef]);

  // Define the manual scrolling check function
  const isManualScrollingStillActive = useCallback(() => {
    return userManuallyScrolledRef.current &&
           (Date.now() - userScrollTimestampRef.current < AUTO_SCROLL_RESTORATION_DELAY);
  }, []);

  // Simplified scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (!messagesContainerRef.current) return;
    
    // Set scrolling flag to prevent scroll handler from interfering
    isScrollingRef.current = true;
    
    // Perform the scroll
    messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    
    // Reset flag after a short delay
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, [messagesContainerRef]);

  // Direct scroll to bottom that forces auto-scroll on
  const directScrollToBottom = useCallback(() => {
    if (!messagesContainerRef.current) return;
    
    // Force auto-scroll back on and reset manual scroll flags
    setAutoScroll(true);
    userManuallyScrolledRef.current = false;
    
    // Perform the scroll
    scrollToBottom();
  }, [messagesContainerRef, setAutoScroll, scrollToBottom]);

  // Scroll to specific message
  const scrollToMessage = useCallback((messageId) => {
    // Don't scroll to message if we're already doing another scroll operation
    if (isScrollingRef.current) return;

    isScrollingRef.current = true;

    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({
        behavior: 'auto',
        block: 'center'
      });
    }

    // Reset scrolling flag after delay
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, []);

  // Simplified scroll handler
  const handleScroll = useCallback((e, autoScroll) => {
    if (isScrollingRef.current) return;

    const container = messagesContainerRef.current;
    if (container && e.target === container) {
      const atBottom = isAtBottom();

      // If we were at the bottom and now we're not, user manually scrolled up
      if (autoScroll && !atBottom) {
        setAutoScroll(false);
        userManuallyScrolledRef.current = true;
        userScrollTimestampRef.current = Date.now();
      }

      // If we're at the bottom again after being scrolled up, restore auto scroll
      if (!autoScroll && atBottom) {
        setAutoScroll(true);
        userManuallyScrolledRef.current = false;
      }

      // Clear new messages indicator when at bottom
      if (atBottom) {
        setHasNewMessages(false);
      }
    }
  }, [isAtBottom, setAutoScroll, setHasNewMessages, messagesContainerRef]);

  return {
    isAtBottom,
    isManualScrollingStillActive,
    scrollToBottom,
    directScrollToBottom,
    scrollToMessage,
    handleScroll,
    userManuallyScrolledRef,
    isScrollingRef
  };
}; 