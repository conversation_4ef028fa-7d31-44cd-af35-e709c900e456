
/* Optional: Add transition effects for color changes */
.eta-component {
    transition: all 0.3s ease;
  }
  
  /* Optional: Custom shadow effects for different states */
  .eta-green {
    box-shadow: 0 1px 3px rgba(34, 197, 94, 0.1);
  }
  
  .eta-yellow {
    box-shadow: 0 1px 3px rgba(250, 204, 21, 0.1);
  }
  
  .eta-red {
    box-shadow: 0 1px 3px rgba(239, 68, 68, 0.1);
  }
  
  /* Optional: Hover effects */
  .eta-component:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  /* Container specific styles */
  .eta-container {
    min-width: 280px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .eta-component {
      padding: 12px;
    }
    
    .eta-component .text-sm {
      font-size: 0.75rem;
    }
  }