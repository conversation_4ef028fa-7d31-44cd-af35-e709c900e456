"use client";

import { Bell, Trash2 } from "lucide-react";
import { useNotifications } from "@/components/Context/NotificationProvider";

export const NotificationDisplay = () => {
  const { notifications, setNotifications } = useNotifications();

  

  const handleDelete = (index) => {
    const updatedNotifications = notifications.filter((_, i) => i !== index);
    setNotifications(updatedNotifications);
  };

  return (
    <div className="mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-6 h-6 text-black" />
            <h2 className="typography-heading-4 font-weight-bold text-black">Notifications</h2>
          </div>
          <span className="px-2.5 py-1 bg-blue-400 text-white typography-body-sm font-weight-medium rounded-full">
            {notifications.length}
          </span>
        </div>
      </div>

      {/* Notification List */}
      <div className="divide-y divide-gray-100">
        {notifications.map((notification, index) => {
          const title = notification?.data?.["pinpoint.notification.title"] || "No Title";
          const body = notification?.data?.["pinpoint.notification.body"] || "No Body";

          return (
            <div
              key={index}
              className="group hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="p-4 border-t border-gray-200">
                <div className="flex items-start justify-between">
                  {/* Notification Content */}
                  <div className="flex-1 min-w-0">
                    <h3 className="typography-body-sm font-weight-semibold text-gray-900 mb-1">{title}</h3>
                    <p className="typography-body-sm text-gray-500 line-clamp-2">{body}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="ml-4 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <button
                      onClick={() => handleDelete(index)}
                      className="p-1 hover:bg-red-100 rounded-full text-red-600 transition-colors duration-200"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Empty State */}
        {notifications.length === 0 && (
          <div className="py-12">
            <div className="flex flex-col items-center justify-center text-center">
              <Bell className="w-12 h-12 text-gray-300 mb-4" />
              <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-2">
                No notifications yet
              </h3>
              <p className="typography-body-sm text-gray-500 max-w-sm">
                When you receive notifications, they&apos;ll show up here.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
